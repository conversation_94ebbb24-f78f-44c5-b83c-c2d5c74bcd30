// 用户管理专用功能

let allUsers = [];
let filteredUsers = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', async function() {
    if (await checkAdminAuth()) {
        initializeUsersPage();
    }
});

// 初始化用户页面
async function initializeUsersPage() {
    await loadUsers();
    initializeSearch();
    createUserModals();
    loadUserStats();
}

// 加载用户数据
async function loadUsers() {
    try {
        console.log('🔍 [USERS] 开始加载用户数据...');
        console.log('🔍 [USERS] Supabase客户端状态:', typeof supabase);

        // 直接查询用户表（跳过RPC函数）
        const result = await supabase
            .from('users')
            .select('*')
            .order('created_at', { ascending: false });

        const users = result.data;
        const error = result.error;

        console.log('🔍 [USERS] 查询结果 - data:', users);
        console.log('🔍 [USERS] 查询结果 - error:', error);

        if (error) {
            console.error('🚫 [USERS] 数据库查询错误:', error);

            // 如果是权限问题，显示友好的错误信息
            if (error.code === 'PGRST116' || error.message.includes('permission denied') || error.message.includes('RLS')) {
                showError('数据库权限配置问题，请检查Supabase RLS策略设置');
                return;
            }

            throw error;
        }

        console.log('🔍 [USERS] 原始用户数据:', users);

        // 过滤掉管理员用户，管理员不显示在用户列表中
        allUsers = (users || []).filter(user => user.user_type !== 'admin');
        console.log('🔍 [USERS] 过滤后的用户数据:', allUsers);

        filteredUsers = [...allUsers];
        displayUsers();
        updateUsersCount();

    } catch (error) {
        console.error('🚫 [USERS] 加载用户失败:', error);

        // 根据错误类型显示不同的错误信息
        let errorMessage = '加载用户数据失败';

        if (error.message.includes('406')) {
            errorMessage = '数据库访问被拒绝，可能是权限配置问题';
        } else if (error.message.includes('network')) {
            errorMessage = '网络连接失败，请检查网络连接';
        } else if (error.message) {
            errorMessage += ': ' + error.message;
        }

        showError(errorMessage);

        // 显示空状态
        allUsers = [];
        filteredUsers = [];
        displayUsers();
        updateUsersCount();
    }
}

// 显示用户列表
function displayUsers() {
    const tbody = document.getElementById('users-tbody');

    if (filteredUsers.length === 0) {
        tbody.innerHTML = '<tr><td colspan="10" style="text-align: center; padding: 40px; color: #666;">暂无用户数据</td></tr>';
        return;
    }

    const html = filteredUsers.map(user => `
        <tr class="user-row">
            <td><strong>${user.username}</strong></td>
            <td>${user.email}</td>
            <td>
                <span class="user-type-badge user-type-${user.user_type}">
                    ${getUserTypeText(user.user_type)}
                </span>
            </td>
            <td>${user.company_name || '-'}</td>
            <td>${user.phone || '-'}</td>
            <td>
                <span class="status-badge ${user.is_active ? 'status-active' : 'status-inactive'}">
                    ${user.is_active ? '活跃' : '禁用'}
                </span>
            </td>
            <td>${formatDate(user.created_at)}</td>
            <td>
                <span class="visit-time" title="最近访问时间">
                    ${formatVisitTime(user.last_visit_at)}
                </span>
            </td>
            <td>
                <span class="visit-count" title="总访问次数">
                    ${formatVisitCount(user.visit_count)}
                </span>
            </td>
            <td>
                <div class="actions">
                    <button class="action-btn btn-primary" onclick="editUser('${user.id}')" title="编辑">✏️</button>
                    <button class="action-btn btn-warning" onclick="toggleUserStatus('${user.id}', ${user.is_active})" title="${user.is_active ? '禁用' : '启用'}">
                        ${user.is_active ? '🚫' : '✅'}
                    </button>
                    <button class="action-btn btn-success" onclick="viewUserDetails('${user.id}')" title="查看详情">👁️</button>
                    <button class="action-btn btn-danger" onclick="deleteUser('${user.id}')" title="删除">🗑️</button>
                </div>
            </td>
        </tr>
    `).join('');

    tbody.innerHTML = html;
}

// 格式化访问时间
function formatVisitTime(visitTime) {
    if (!visitTime) {
        return '<span style="color: #999;">从未访问</span>';
    }

    const date = new Date(visitTime);
    const now = new Date();
    const diffMs = now - date;
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor(diffMs / (1000 * 60));

    let timeAgo = '';
    if (diffDays > 0) {
        timeAgo = `${diffDays}天前`;
    } else if (diffHours > 0) {
        timeAgo = `${diffHours}小时前`;
    } else if (diffMinutes > 0) {
        timeAgo = `${diffMinutes}分钟前`;
    } else {
        timeAgo = '刚刚';
    }

    const formattedDate = date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });

    return `<span title="${formattedDate}">${timeAgo}</span>`;
}

// 格式化访问次数
function formatVisitCount(count) {
    if (!count || count === 0) {
        return '<span style="color: #999;">0</span>';
    }

    let color = '#666';
    if (count >= 100) {
        color = '#28a745'; // 绿色 - 活跃用户
    } else if (count >= 50) {
        color = '#ffc107'; // 黄色 - 中等活跃
    } else if (count >= 10) {
        color = '#17a2b8'; // 蓝色 - 一般活跃
    }

    return `<span style="color: ${color}; font-weight: bold;">${count}</span>`;
}

// 获取用户类型文本
function getUserTypeText(userType) {
    switch (userType) {
        case 'premium': return '高级用户';
        case 'privileged': return '特许用户';
        case 'admin': return '管理员';
        default: return '普通用户';
    }
}

// 初始化搜索功能
function initializeSearch() {
    const searchInput = document.getElementById('search-input');
    const userTypeFilter = document.getElementById('user-type-filter');
    const statusFilter = document.getElementById('status-filter');
    
    // 防抖搜索
    const debouncedSearch = debounce(searchUsers, 300);
    searchInput.addEventListener('input', debouncedSearch);
    
    // 筛选器变化
    userTypeFilter.addEventListener('change', searchUsers);
    statusFilter.addEventListener('change', searchUsers);
    
    // 回车搜索
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchUsers();
        }
    });
}

// 搜索用户
function searchUsers() {
    const searchTerm = document.getElementById('search-input').value.trim().toLowerCase();
    const userTypeFilter = document.getElementById('user-type-filter').value;
    const statusFilter = document.getElementById('status-filter').value;
    
    filteredUsers = allUsers.filter(user => {
        // 文本搜索
        const textMatch = !searchTerm || 
            user.username.toLowerCase().includes(searchTerm) ||
            user.email.toLowerCase().includes(searchTerm) ||
            (user.company_name && user.company_name.toLowerCase().includes(searchTerm)) ||
            (user.phone && user.phone.includes(searchTerm));
        
        // 用户类型筛选
        const typeMatch = !userTypeFilter || user.user_type === userTypeFilter;
        
        // 状态筛选
        const statusMatch = !statusFilter || user.is_active.toString() === statusFilter;
        
        return textMatch && typeMatch && statusMatch;
    });
    
    displayUsers();
    updateUsersCount();
}

// 重置搜索
function resetSearch() {
    document.getElementById('search-input').value = '';
    document.getElementById('user-type-filter').value = '';
    document.getElementById('status-filter').value = '';
    
    filteredUsers = [...allUsers];
    displayUsers();
    updateUsersCount();
}

// 更新用户数量显示
function updateUsersCount() {
    const countElement = document.getElementById('users-count');
    if (countElement) {
        countElement.textContent = filteredUsers.length;
    }
}

// 加载用户统计
function loadUserStats() {
    // 统计普通用户（不包括管理员）
    const totalUsers = allUsers.length;
    const premiumUsers = allUsers.filter(u => u.user_type === 'premium').length;
    const privilegedUsers = allUsers.filter(u => u.user_type === 'privileged').length;
    // 管理员不显示在列表中，统计为0
    const adminUsers = 0;

    document.getElementById('total-users').textContent = totalUsers;
    document.getElementById('premium-users').textContent = premiumUsers;
    document.getElementById('privileged-users').textContent = privilegedUsers;
    document.getElementById('admin-users').textContent = adminUsers;
}

// 显示添加用户模态框
async function showAddUserModal() {
    document.getElementById('user-form').reset();
    document.getElementById('user-id').value = '';
    document.getElementById('user-modal-title').textContent = '添加用户';

    // 新增用户时密码是必填的
    document.getElementById('edit-password').required = true;
    document.getElementById('edit-confirm-password').required = true;
    document.querySelector('label[for="edit-password"]').innerHTML = '密码 *';
    document.querySelector('label[for="edit-confirm-password"]').innerHTML = '确认密码 *';

    // 检查是否已存在管理员，如果存在则隐藏管理员选项
    await updateUserTypeOptions(false);

    showModal('user-modal');
}

// 更新用户类型选项
async function updateUserTypeOptions(isEdit = false, currentUserType = null) {
    try {
        // 直接查询用户表检查是否已存在管理员
        const { data: allUsers, error } = await supabase
            .from('users')
            .select('*')
            .eq('user_type', 'admin')
            .eq('is_active', true);

        if (error) throw error;

        const adminUsers = allUsers || [];

        const userTypeSelect = document.getElementById('edit-user-type');
        const adminOption = userTypeSelect.querySelector('option[value="admin"]');

        if (isEdit) {
            // 编辑模式：如果当前用户就是管理员，或者没有其他管理员，则显示管理员选项
            if (currentUserType === 'admin' || adminUsers.length === 0) {
                adminOption.style.display = 'block';
            } else {
                adminOption.style.display = 'none';
            }
        } else {
            // 添加模式：管理员选项始终隐藏（不允许通过后台添加管理员）
            adminOption.style.display = 'none';
        }

    } catch (error) {
        console.error('检查管理员用户失败:', error);
        // 出错时默认隐藏管理员选项
        const adminOption = document.getElementById('edit-user-type').querySelector('option[value="admin"]');
        if (adminOption) {
            adminOption.style.display = 'none';
        }
    }
}

// 编辑用户
async function editUser(userId) {
    try {
        // 先查询用户是否存在
        const { data: users, error: queryError } = await supabase
            .from('users')
            .select('*')
            .eq('id', userId);

        if (queryError) {
            console.error('查询用户详情失败:', queryError);

            // 根据错误类型显示不同的错误信息
            let errorMessage = '加载用户详情失败';

            if (queryError.code === 'PGRST116') {
                errorMessage = '用户不存在或已被删除';
            } else if (queryError.message.includes('406')) {
                errorMessage = '数据库访问被拒绝，可能是权限配置问题';
            } else if (queryError.message.includes('permission denied')) {
                errorMessage = '没有权限访问用户数据';
            } else if (queryError.message) {
                errorMessage += ': ' + queryError.message;
            }

            showError(errorMessage);
            return;
        }

        // 检查是否找到用户
        if (!users || users.length === 0) {
            showError('用户不存在或已被删除');
            return;
        }

        const user = users[0]; // 取第一个用户

        // 填充表单
        fillUserForm(user);
        document.getElementById('user-modal-title').textContent = '编辑用户';

        // 编辑用户时密码是可选的
        document.getElementById('edit-password').required = false;
        document.getElementById('edit-confirm-password').required = false;
        document.querySelector('label[for="edit-password"]').innerHTML = '新密码';
        document.querySelector('label[for="edit-confirm-password"]').innerHTML = '确认新密码';

        // 更新用户类型选项（编辑模式）
        await updateUserTypeOptions(true, user.user_type);

        showModal('user-modal');

    } catch (error) {
        console.error('加载用户详情失败:', error);
        showError('加载用户详情失败: ' + (error.message || '未知错误'));
    }
}

// 填充用户表单
function fillUserForm(user) {
    document.getElementById('user-id').value = user.id;
    document.getElementById('edit-username').value = user.username;
    document.getElementById('edit-email').value = user.email;
    document.getElementById('edit-user-type').value = user.user_type;
    document.getElementById('edit-company-name').value = user.company_name || '';
    document.getElementById('edit-phone').value = user.phone || '';
    document.getElementById('edit-is-active').checked = user.is_active;

    // 清空密码字段（编辑时不显示现有密码）
    document.getElementById('edit-password').value = '';
    document.getElementById('edit-confirm-password').value = '';
}

// 切换用户状态
async function toggleUserStatus(userId, currentStatus) {
    const action = currentStatus ? '禁用' : '启用';
    
    confirmAction(`确定要${action}这个用户吗？`, async () => {
        try {
            const { error } = await supabase
                .from('users')
                .update({ is_active: !currentStatus })
                .eq('id', userId);
            
            if (error) throw error;
            
            showSuccess(`用户${action}成功`);
            loadUsers();
            
        } catch (error) {
            console.error(`${action}用户失败:`, error);
            showError(`${action}用户失败`);
        }
    });
}

// 查看用户详情
async function viewUserDetails(userId) {
    try {
        // 先查询用户是否存在
        const { data: users, error: queryError } = await supabase
            .from('users')
            .select('*')
            .eq('id', userId);

        if (queryError) {
            console.error('查询用户详情失败:', queryError);

            let errorMessage = '加载用户详情失败';
            if (queryError.code === 'PGRST116') {
                errorMessage = '用户不存在或已被删除';
            } else if (queryError.message.includes('permission denied')) {
                errorMessage = '没有权限访问用户数据';
            } else if (queryError.message) {
                errorMessage += ': ' + queryError.message;
            }

            showError(errorMessage);
            return;
        }

        // 检查是否找到用户
        if (!users || users.length === 0) {
            showError('用户不存在或已被删除');
            return;
        }

        const user = users[0];

        // 获取用户下载记录
        const { data: downloads } = await supabase
            .from('download_records')
            .select('*, products(product_name)')
            .eq('user_id', userId)
            .order('downloaded_at', { ascending: false })
            .limit(10);

        showUserDetails(user, downloads || []);

    } catch (error) {
        console.error('加载用户详情失败:', error);
        showError('加载用户详情失败: ' + (error.message || '未知错误'));
    }
}

// 显示用户详情
function showUserDetails(user, downloads) {
    const detailsHtml = `
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
            <div>
                <h4>基本信息</h4>
                <p><strong>用户名:</strong> ${user.username}</p>
                <p><strong>邮箱:</strong> ${user.email}</p>
                <p><strong>用户类型:</strong> ${getUserTypeText(user.user_type)}</p>
                <p><strong>状态:</strong> ${user.is_active ? '活跃' : '禁用'}</p>
            </div>
            <div>
                <h4>联系信息</h4>
                <p><strong>公司名称:</strong> ${user.company_name || '-'}</p>
                <p><strong>手机号:</strong> ${user.phone || '-'}</p>
                <p><strong>注册时间:</strong> ${formatDate(user.created_at)}</p>
                <p><strong>最后更新:</strong> ${formatDate(user.updated_at)}</p>
            </div>
        </div>
        
        <div>
            <h4>下载记录 (最近10条)</h4>
            ${downloads.length > 0 ? `
                <div style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; border-radius: 4px;">
                    <table style="width: 100%; font-size: 12px;">
                        <thead style="background: #f8f9fa;">
                            <tr>
                                <th style="padding: 8px; border-bottom: 1px solid #ddd;">产品名称</th>
                                <th style="padding: 8px; border-bottom: 1px solid #ddd;">文件类型</th>
                                <th style="padding: 8px; border-bottom: 1px solid #ddd;">下载时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${downloads.map(download => `
                                <tr>
                                    <td style="padding: 8px; border-bottom: 1px solid #eee;">${download.products?.product_name || '未知产品'}</td>
                                    <td style="padding: 8px; border-bottom: 1px solid #eee;">${download.file_type}</td>
                                    <td style="padding: 8px; border-bottom: 1px solid #eee;">${formatDate(download.downloaded_at)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            ` : '<p style="color: #666; font-style: italic;">暂无下载记录</p>'}
        </div>
    `;
    
    document.getElementById('user-details').innerHTML = detailsHtml;
    showModal('user-details-modal');
}

// 删除用户
function deleteUser(userId) {
    confirmAction('确定要删除这个用户吗？此操作不可恢复。', async () => {
        try {
            const { error } = await supabase
                .from('users')
                .delete()
                .eq('id', userId);
            
            if (error) throw error;
            
            showSuccess('用户删除成功');
            loadUsers();
            
        } catch (error) {
            console.error('删除用户失败:', error);
            showError('删除用户失败');
        }
    });
}

// 保存用户
async function saveUser() {
    const form = document.getElementById('user-form');
    const formData = new FormData(form);
    const userId = document.getElementById('user-id').value;
    
    // 获取密码字段
    const password = formData.get('password').trim();
    const confirmPassword = formData.get('confirm_password').trim();

    // 构建用户数据
    const userData = {
        username: formData.get('username').trim(),
        email: formData.get('email').trim(),
        user_type: formData.get('user_type'),
        company_name: formData.get('company_name').trim(),
        phone: formData.get('phone').trim(),
        is_active: formData.get('is_active') === 'on'
    };

    // 验证必填字段
    if (!userData.username || !userData.email || !userData.user_type) {
        showError('请填写必填字段');
        return;
    }

    // 验证密码（新增用户时必填）
    if (!userId) {
        if (!password) {
            showError('新增用户时密码不能为空');
            return;
        }
        if (password.length < 6) {
            showError('密码长度不能少于6位');
            return;
        }
        if (password !== confirmPassword) {
            showError('两次输入的密码不一致');
            return;
        }
    } else if (password) {
        // 编辑用户时，如果填写了密码则验证
        if (password.length < 6) {
            showError('密码长度不能少于6位');
            return;
        }
        if (password !== confirmPassword) {
            showError('两次输入的密码不一致');
            return;
        }
    }

    // 验证管理员唯一性 - 不允许通过后台添加管理员
    if (userData.user_type === 'admin') {
        showError('不允许通过后台添加或修改管理员账户');
        return;
    }

    // 验证邮箱格式
    if (!validateEmail(userData.email)) {
        showError('邮箱格式不正确');
        return;
    }
    
    // 验证手机号格式（如果填写了）
    if (userData.phone && !validatePhone(userData.phone)) {
        showError('手机号格式不正确');
        return;
    }
    
    try {
        let result;

        if (userId) {
            // 更新用户
            if (password) {
                // 使用自定义认证系统，管理员可以直接修改密码
                console.log('🔧 [ADMIN] 管理员更新用户密码:', userData.email);

                // 计算密码哈希（与前端认证系统保持一致）
                const passwordHash = btoa(password + 'chunsheng_salt_2024');
                userData.password_hash = passwordHash;

                console.log('🔧 [ADMIN] 密码哈希已生成');
            }

            // 更新用户表数据
            result = await supabase
                .from('users')
                .update(userData)
                .eq('id', userId);
        } else {
            // 添加用户 - 使用自定义认证系统
            console.log('🔧 [ADMIN] 添加新用户:', userData.email);

            // 检查邮箱是否已存在
            const { data: existingUser } = await supabase
                .from('users')
                .select('email')
                .eq('email', userData.email)
                .single();

            if (existingUser) {
                throw new Error('该邮箱已被注册');
            }

            // 计算密码哈希
            const passwordHash = btoa(password + 'chunsheng_salt_2024');
            userData.password_hash = passwordHash;
            userData.is_active = true;

            // 直接在用户表中创建记录
            result = await supabase
                .from('users')
                .insert([userData])
                .select();

            console.log('🔧 [ADMIN] 新用户创建成功');
        }

        if (result.error) throw result.error;

        showSuccess(userId ? '用户更新成功' : '用户添加成功');
        hideModal('user-modal');
        loadUsers();

    } catch (error) {
        console.error('保存用户失败:', error);
        showError('保存用户失败: ' + error.message);
    }
}

// 导出用户数据
function exportUsers() {
    if (filteredUsers.length === 0) {
        showError('没有数据可导出');
        return;
    }
    
    const exportData = filteredUsers.map(user => ({
        '用户名': user.username,
        '邮箱': user.email,
        '用户类型': getUserTypeText(user.user_type),
        '公司名称': user.company_name || '',
        '手机号': user.phone || '',
        '状态': user.is_active ? '活跃' : '禁用',
        '注册时间': formatDate(user.created_at)
    }));
    
    exportToCSV(exportData, `用户数据_${new Date().toISOString().split('T')[0]}.csv`);
}

// 创建用户模态框
function createUserModals() {
    // 用户编辑模态框
    const userModalHtml = `
        <div id="user-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="user-modal-title">添加用户</h2>
                    <span class="close">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="user-form">
                        <input type="hidden" id="user-id">
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="edit-username">用户名 *</label>
                                <input type="text" id="edit-username" name="username" required>
                            </div>
                            <div class="form-group">
                                <label for="edit-email">邮箱 *</label>
                                <input type="email" id="edit-email" name="email" required>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="edit-password">密码 *</label>
                                <input type="password" id="edit-password" name="password" required>
                                <small style="color: #666; font-size: 12px;">新增用户时必填，编辑时留空表示不修改密码</small>
                            </div>
                            <div class="form-group">
                                <label for="edit-confirm-password">确认密码 *</label>
                                <input type="password" id="edit-confirm-password" name="confirm_password" required>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="edit-user-type">用户类型 *</label>
                                <select id="edit-user-type" name="user_type" required>
                                    <option value="">请选择</option>
                                    <option value="premium">高级用户</option>
                                    <option value="privileged">特许用户</option>
                                    <option value="admin">管理员</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="edit-company-name">公司名称</label>
                                <input type="text" id="edit-company-name" name="company_name">
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="edit-phone">手机号</label>
                                <input type="tel" id="edit-phone" name="phone">
                            </div>
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="edit-is-active" name="is_active" checked>
                                    启用用户
                                </label>
                            </div>
                        </div>
                        
                        <div style="text-align: right; margin-top: 30px;">
                            <button type="button" class="btn btn-secondary" onclick="hideModal('user-modal')">取消</button>
                            <button type="button" class="btn btn-primary" onclick="saveUser()">保存</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div id="user-details-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>用户详情</h2>
                    <span class="close">&times;</span>
                </div>
                <div class="modal-body">
                    <div id="user-details"></div>
                </div>
            </div>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', userModalHtml);
}
