<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安徽春晟机械有限公司_减震器冲压件_减震器冲压件厂家</title>
    <meta name="description" content="安徽春晟机械有限公司是专业从事减震器冲压件设计与生产的企业。公司坐落于安徽省广德经济开发区，地处苏浙皖三省交界处，交通便利，环境优雅。">
    <meta name="keywords" content="安徽春晟机械有限公司,减震器冲压件,减震器冲压件厂家">
    
    <!-- 原网站样式 -->
    <link href="pcstyle.css" rel="stylesheet" type="text/css">
    <link href="reset.css" rel="stylesheet" type="text/css">
    <link href="32_Pc_zh-CN.css" rel="stylesheet" type="text/css">
    
    <!-- 自定义样式 -->
    <link href="css/custom.css" rel="stylesheet" type="text/css">
    <link href="css/customer-service-chat.css" rel="stylesheet" type="text/css">

    <!-- Supabase -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>

    <!-- jQuery -->
    <script src="jquery-3.6.3.min.js"></script>
</head>
<body>
    <!-- 头部区域 -->
    <header class="header-section">
        <!-- Logo区域 -->
        <div class="logo-section" style="background-color: rgb(37, 37, 37); padding: 10px 0;">
            <div class="container" style="width: 1200px; margin: 0 auto; display: flex; justify-content: space-between; align-items: center;">
                <div class="logo">
                    <img src="16792504.png" alt="安徽春晟机械有限公司" style="height: 60px;">
                </div>
                <div class="user-actions">
                    <a href="login.html" class="login-btn" style="color: white; margin-right: 15px;">登录</a>
                    <a href="register.html" class="register-btn" style="background: #be131b; color: white; padding: 5px 15px;">注册</a>
                </div>
            </div>
        </div>
        
        <!-- 导航栏 -->
        <nav class="navigation" style="background-color: #F5F5F5; border-bottom: 1px solid #ddd;">
            <div class="container" style="width: 1200px; margin: 0 auto;">
                <ul class="nav-menu" style="display: flex; list-style: none; margin: 0; padding: 0;">
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="index.html" style="display: block; padding: 20px; color: #be131b; text-decoration: none; transition: all 0.3s; font-weight: bold;">网站首页</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="about.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">关于我们</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="factory.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">企业展示</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="factory.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">工厂设备</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="products.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">产品中心</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="news.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">新闻中心</a>
                    </li>
                    <li style="flex: 1; text-align: center;">
                        <a href="contact.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">联系我们</a>
                    </li>
                </ul>
            </div>
        </nav>
    </header>

    <!-- 轮播图区域 -->
    <section class="hero-section">
        <div class="slider-container" style="position: relative; height: 600px; overflow: hidden;">
            <div class="slide active" style="position: absolute; width: 100%; height: 100%; background: url('16636630.jpg') center/cover;">
                <div class="slide-content" style="position: absolute; left: 100px; top: 50%; transform: translateY(-50%); color: white; z-index: 2;">
                    <h1 style="font-size: 48px; margin-bottom: 20px;">专业生产减震器冲压件</h1>
                    <p style="font-size: 18px; margin-bottom: 30px;">安徽春晟机械有限公司 - 专业从事减震器冲压件设计与生产</p>
                    <a href="products.html" class="cta-button" style="background: #be131b; color: white; padding: 12px 30px; text-decoration: none; border-radius: 3px;">了解更多</a>
                </div>
            </div>
            <div class="slide" style="position: absolute; width: 100%; height: 100%; background: url('16719895.jpg') center/cover;">
                <div class="slide-content" style="position: absolute; left: 100px; top: 50%; transform: translateY(-50%); color: white; z-index: 2;">
                    <h1 style="font-size: 48px; margin-bottom: 20px;">高品质汽车零部件制造</h1>
                    <p style="font-size: 18px; margin-bottom: 30px;">采用先进的冲压工艺，确保产品质量稳定可靠</p>
                    <a href="products.html" class="cta-button" style="background: #be131b; color: white; padding: 12px 30px; text-decoration: none; border-radius: 3px;">产品展示</a>
                </div>
            </div>


            <!-- 轮播控制按钮 -->
            <button class="slider-prev" onclick="changeSlide(-1)" style="position: absolute; left: 20px; top: 50%; transform: translateY(-50%); background: rgba(255,255,255,0.3); border: none; color: white; padding: 15px; border-radius: 50%; cursor: pointer; z-index: 3;">‹</button>
            <button class="slider-next" onclick="changeSlide(1)" style="position: absolute; right: 20px; top: 50%; transform: translateY(-50%); background: rgba(255,255,255,0.3); border: none; color: white; padding: 15px; border-radius: 50%; cursor: pointer; z-index: 3;">›</button>

            <!-- 轮播指示器 -->
            <div class="slider-indicators" style="position: absolute; bottom: 20px; left: 50%; transform: translateX(-50%); display: flex; gap: 10px; z-index: 3;">
                <span class="indicator active" onclick="currentSlide(1)" style="width: 12px; height: 12px; border-radius: 50%; background: white; cursor: pointer; opacity: 1;"></span>
                <span class="indicator" onclick="currentSlide(2)" style="width: 12px; height: 12px; border-radius: 50%; background: white; cursor: pointer; opacity: 0.5;"></span>
            </div>
        </div>
    </section>

    <!-- 公司介绍区域 -->
    <section class="company-intro" style="padding: 80px 0; background: white;">
        <div class="container" style="width: 1200px; margin: 0 auto; display: flex; align-items: center;">
            <div class="intro-content" style="flex: 1; padding-right: 50px;">
                <h2 style="font-size: 32px; color: #222; margin-bottom: 20px;">安徽春晟机械有限公司</h2>
                <p style="font-size: 16px; line-height: 1.8; color: #444; margin-bottom: 20px;">
                    本公司系专业从事减震器冲压件设计与生产的企业。公司坐落于安徽省广德经济开发区，地处苏浙皖三省交界处，交通便利，环境优雅。
                    距离上海230公里，杭州100公里，宁波280公里，南京230公里，拥有天然的地理优势。
                </p>
                <p style="font-size: 16px; line-height: 1.8; color: #444; margin-bottom: 30px;">
                    工厂占地40亩（建筑面积22000㎡），包括综合办公楼，生产车间，模具仓库，材料仓库，物流区域，员工宿舍，员工食堂，以及员工文娱场所等。
                </p>
                <div class="action-buttons">
                    <a href="about.html" style="background: #be131b; color: white; padding: 10px 20px; text-decoration: none; margin-right: 15px;">企业文化</a>
                    <a href="about.html" style="background: #be131b; color: white; padding: 10px 20px; text-decoration: none; margin-right: 15px;">发展历程</a>
                    <a href="about.html" style="background: #be131b; color: white; padding: 10px 20px; text-decoration: none;">组织架构</a>
                </div>
            </div>
            <div class="intro-image" style="flex: 1;">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                    <img src="16028205.jpg" alt="公司厂房" style="width: 100%; height: 200px; object-fit: cover; border-radius: 5px;">
                    <img src="16719909.jpg" alt="生产车间" style="width: 100%; height: 200px; object-fit: cover; border-radius: 5px;">
                    <img src="16719937.jpg" alt="产品展示" style="width: 100%; height: 200px; object-fit: cover; border-radius: 5px;">
                    <img src="16719962.jpg" alt="设备展示" style="width: 100%; height: 200px; object-fit: cover; border-radius: 5px;">
                </div>
            </div>
        </div>
    </section>

    <!-- 产品中心区域 -->
    <section class="product-center-section" style="padding: 60px 0; background: #f8f9fa;">
        <div class="container" style="width: 1200px; margin: 0 auto;">
            <!-- 产品中心标题 -->
            <div class="section-header" style="text-align: center; margin-bottom: 50px;">
                <h2 style="font-size: 32px; color: #222; margin-bottom: 15px;">产品中心</h2>
                <p style="font-size: 16px; color: #666;">专业生产各类减震器冲压件产品</p>

                <!-- 权限状态指示器 -->
                <div id="permission-status" style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 6px; display: inline-block;">
                    <span style="font-size: 14px; color: #666;">当前权限：</span>
                    <span id="permission-level" style="font-weight: bold;">加载中...</span>
                    <div id="permission-description" style="font-size: 12px; color: #999; margin-top: 4px;"></div>
                </div>
            </div>
            <div class="product-center-layout" style="display: flex; gap: 30px; min-height: 600px;">

                <!-- 左侧分类菜单 -->
                <div class="category-sidebar" style="width: 300px; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden;">
                    <!-- 分类标题 -->
                    <div class="category-header" style="background: #be131b; color: white; padding: 20px; text-align: center;">
                        <h2 style="margin: 0; font-size: 24px; font-weight: bold;">产品分类</h2>
                    </div>

                    <!-- 分类列表 -->
                    <div class="category-list">
                        <div class="category-item active" data-category="支架（座）类" style="padding: 15px 20px; border-bottom: 1px dotted #ddd; cursor: pointer; transition: all 0.3s; background: #f8f9fa; border-left: 4px solid #be131b;">
                            <span style="color: #be131b; font-weight: bold;">支架（座）类</span>
                            <i style="float: right; color: #be131b;">▶</i>
                        </div>

                        <div class="category-item" data-category="固定圈（防护套）类" style="padding: 15px 20px; border-bottom: 1px dotted #ddd; cursor: pointer; transition: all 0.3s;">
                            <span style="color: #666;">固定圈（防护套）类</span>
                            <i style="float: right; color: #ccc;">▶</i>
                        </div>

                        <div class="category-item" data-category="支耳（板）类" style="padding: 15px 20px; border-bottom: 1px dotted #ddd; cursor: pointer; transition: all 0.3s;">
                            <span style="color: #666;">支耳（板）类</span>
                            <i style="float: right; color: #ccc;">▶</i>
                        </div>

                        <div class="category-item" data-category="弹簧盘类" style="padding: 15px 20px; border-bottom: 1px dotted #ddd; cursor: pointer; transition: all 0.3s;">
                            <span style="color: #666;">弹簧盘类</span>
                            <i style="float: right; color: #ccc;">▶</i>
                        </div>

                        <div class="category-item" data-category="防尘盖（顶板）类" style="padding: 15px 20px; border-bottom: 1px dotted #ddd; cursor: pointer; transition: all 0.3s;">
                            <span style="color: #666;">防尘盖（顶板）类</span>
                            <i style="float: right; color: #ccc;">▶</i>
                        </div>

                        <div class="category-item" data-category="其它类" style="padding: 15px 20px; border-bottom: 1px dotted #ddd; cursor: pointer; transition: all 0.3s;">
                            <span style="color: #666;">其它类</span>
                            <i style="float: right; color: #ccc;">▶</i>
                        </div>
                    </div>

                    <!-- 筛选功能区域 -->
                    <div class="filter-section" style="padding: 20px; border-top: 2px solid #f0f0f0; background: #fafafa;">
                        <div style="background: #be131b; color: white; padding: 10px; text-align: center; margin-bottom: 15px; border-radius: 4px;">
                            <span style="font-weight: bold; font-size: 16px;">车型查询</span>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; color: #333; font-weight: bold;">品牌：</label>
                            <input type="text" id="brand-filter" placeholder="输入品牌名称" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; box-sizing: border-box;">
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; color: #333; font-weight: bold;">车型：</label>
                            <input type="text" id="model-filter" placeholder="输入车型" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; box-sizing: border-box;">
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; color: #333; font-weight: bold;">产品：</label>
                            <input type="text" id="product-filter" placeholder="输入产品名称或编号" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; box-sizing: border-box;">
                        </div>

                        <div style="text-align: center; margin-top: 15px;">
                            <button onclick="applyHomeFilters()" style="background: #be131b; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; font-size: 14px; margin-right: 10px; transition: background 0.3s;" onmouseover="this.style.background='#a01018'" onmouseout="this.style.background='#be131b'">
                                查询
                            </button>
                            <button onclick="resetHomeFilters()" style="background: #666; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; font-size: 14px; transition: background 0.3s;" onmouseover="this.style.background='#555'" onmouseout="this.style.background='#666'">
                                重置
                            </button>
                        </div>

                        <div style="margin-top: 10px; font-size: 12px; color: #999; text-align: center;">
                            通过输入框模糊查询，如：奥（品牌或车型包含"奥"字都满足条件）
                        </div>
                    </div>
                </div>

                <!-- 右侧产品展示区域 -->
                <div class="product-display" style="flex: 1; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); padding: 30px;">
                    <div id="featured-products" class="featured-products" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                        <!-- 产品将通过JavaScript动态加载 -->
                    </div>
                    <div class="text-center" style="margin-top: 30px; text-align: center;">
                        <a href="products.html" style="background: #be131b; color: white; padding: 12px 30px; text-decoration: none; border-radius: 3px;">查看更多产品</a>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <!-- 页脚 -->
    <footer style="background: #333; color: white; padding: 40px 0;">
        <div class="container" style="width: 1200px; margin: 0 auto; text-align: center;">
            <p>&copy; 2024 安徽春晟机械有限公司. 保留所有权利.</p>
            <p>地址：安徽省广德经济开发区 | 专业从事减震器冲压件设计与生产</p>
        </div>
    </footer>

    <!-- 产品中心样式 -->
    <style>
        .category-item {
            transition: all 0.3s ease;
        }

        .category-item:hover {
            background: #f8f9fa !important;
            border-left: 4px solid #be131b !important;
        }

        .category-item:hover span {
            color: #be131b !important;
            font-weight: bold !important;
        }

        .category-item:hover i {
            color: #be131b !important;
        }

        .category-item.active {
            background: #f8f9fa !important;
            border-left: 4px solid #be131b !important;
        }

        .category-item.active span {
            color: #be131b !important;
            font-weight: bold !important;
        }

        .category-item.active i {
            color: #be131b !important;
        }

        .product-item {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .product-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .product-item img {
            width: 100%;
            height: 150px;
            object-fit: cover;
        }

        .product-item .product-info {
            padding: 15px;
        }

        .product-item .product-name {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
            text-align: center;
        }

        .product-item .product-code {
            font-size: 12px;
            color: #666;
            text-align: center;
        }

        @media (max-width: 768px) {
            .product-center-layout {
                flex-direction: column !important;
            }

            .category-sidebar {
                width: 100% !important;
                margin-bottom: 20px;
            }

            .product-display {
                padding: 20px !important;
            }

            .container {
                width: 95% !important;
                padding: 0 10px !important;
            }
        }
    </style>

    <!-- JavaScript -->
    <script src="js/supabase-init.js"></script>
    <script src="js/supabase-auth.js"></script>
    <script src="js/main.js"></script>
    <script>
        // 轮播图功能
        let currentSlideIndex = 0;
        const slides = document.querySelectorAll('.slide');
        const indicators = document.querySelectorAll('.indicator');

        function showSlide(index) {
            slides.forEach((slide, i) => {
                slide.classList.toggle('active', i === index);
            });
            indicators.forEach((indicator, i) => {
                indicator.style.opacity = i === index ? '1' : '0.5';
            });
        }

        function changeSlide(direction) {
            currentSlideIndex += direction;
            if (currentSlideIndex >= slides.length) currentSlideIndex = 0;
            if (currentSlideIndex < 0) currentSlideIndex = slides.length - 1;
            showSlide(currentSlideIndex);
        }

        function currentSlide(index) {
            currentSlideIndex = index - 1;
            showSlide(currentSlideIndex);
        }

        // 自动轮播
        setInterval(() => {
            changeSlide(1);
        }, 5000);

        // 产品中心功能
        let allProducts = [];
        let currentCategory = '支架（座）类';

        // 产品分类映射（数据库代码 -> 中文名称）
        const categoryMapping = {
            'ZJ': '支架（座）类',
            'GD': '固定圈（防护套）类',
            'ZE': '支耳（板）类',
            'TP': '弹簧盘类',
            'FC': '防尘盖（顶板）类',
            'QT': '其它类',
            // 兼容旧数据
            'DB': '防尘盖（顶板）类',
            'ZC': '支架（座）类'
        };

        // 中文分类 -> 数据库代码的反向映射
        const reverseCategoryMapping = {
            '支架（座）类': ['ZJ', 'ZC'],
            '固定圈（防护套）类': ['GD'],
            '支耳（板）类': ['ZE'],
            '弹簧盘类': ['TP'],
            '防尘盖（顶板）类': ['FC', 'DB'],
            '其它类': ['QT']
        };

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 延迟初始化，确保权限系统已加载
            setTimeout(() => {
                initializeProductCenter();
                // 强制刷新权限状态
                forceRefreshPermissionStatus();
            }, 1000);
        });

        // 监听权限状态变化
        window.addEventListener('authReady', function(event) {
            console.log('权限系统已就绪，更新产品显示');
            setTimeout(() => {
                updatePermissionStatus();
                displayProductsByCategory(currentCategory);
            }, 500); // 延迟一点确保认证系统完全就绪
        });

        // 监听用户登录状态变化
        setInterval(() => {
            const currentDisplayedType = document.getElementById('permission-level')?.textContent;
            const actualUserType = getCurrentUserType();
            const actualBadgeText = getPermissionBadgeText(actualUserType);

            // 检查显示的权限是否与实际权限不符
            if (currentDisplayedType && !currentDisplayedType.includes(actualBadgeText.split(' ')[1])) {
                console.log('检测到权限状态变化，更新显示', {
                    displayed: currentDisplayedType,
                    actual: actualBadgeText
                });
                updatePermissionStatus();
                displayProductsByCategory(currentCategory);
            }
        }, 2000);

        // 初始化产品中心
        async function initializeProductCenter() {
            try {
                // 更新权限状态显示
                updatePermissionStatus();

                // 加载产品数据
                await loadProducts();

                // 初始化分类切换功能
                initializeCategorySwitch();

                // 显示默认分类的产品
                displayProductsByCategory(currentCategory);

            } catch (error) {
                console.error('初始化产品中心失败:', error);
            }
        }

        // 更新权限状态显示
        function updatePermissionStatus() {
            const userType = getCurrentUserType(); // 这会自动从本地存储恢复权限
            const permissionLevel = document.getElementById('permission-level');
            const permissionDescription = document.getElementById('permission-description');

            console.log('🔄 更新权限状态显示:', userType);

            if (permissionLevel && permissionDescription) {
                const badgeText = getPermissionBadgeText(userType);
                const badgeColor = getPermissionBadgeColor(userType);

                permissionLevel.innerHTML = `<span style="color: ${badgeColor};">${badgeText}</span>`;

                // 设置权限描述
                let description = '';
                switch(userType) {
                    case 'admin':
                        description = '拥有完整系统权限，可查看详情、下载所有资料';
                        break;
                    case 'privileged':
                        description = '可查看详情、下载基础资料和PDF文档，可访问产品中心高级搜索';
                        break;
                    case 'premium':
                        description = '可查看产品详情和下载基础资料';
                        break;
                    case 'normal':
                        description = '可查看产品基础详情，管理个人信息';
                        break;
                    case 'guest':
                    default:
                        description = '可浏览基本产品信息，登录后可查看更多详情';
                        break;
                }

                permissionDescription.textContent = description;
            }

            // 如果权限发生变化，重新显示产品
            if (typeof currentCategory !== 'undefined') {
                displayProductsByCategory(currentCategory);
            }
        }

        // 强制刷新权限状态
        function forceRefreshPermissionStatus() {
            console.log('🔄 强制刷新权限状态');

            // 检查所有可能的权限来源
            let userType = 'guest';
            let userInfo = null;

            // 1. 检查CustomAuth认证系统
            if (window.auth && window.auth.isInitialized) {
                userType = window.auth.getUserType();
                userInfo = window.auth.getCurrentUser();
                console.log('从CustomAuth获取:', { userType, userInfo });
            }

            // 2. 如果CustomAuth没有有效用户，检查本地存储
            if (userType === 'guest') {
                try {
                    const savedUser = localStorage.getItem('simple_auth_user');
                    const loginTime = localStorage.getItem('simple_auth_login_time');

                    if (savedUser && loginTime) {
                        const now = Date.now();
                        const loginTimestamp = parseInt(loginTime);
                        const expireTime = 24 * 60 * 60 * 1000; // 24小时

                        if (now - loginTimestamp < expireTime) {
                            const user = JSON.parse(savedUser);
                            userType = user.user_type || 'premium';
                            userInfo = user;
                            console.log('从本地存储获取:', { userType, userInfo });

                            // 更新全局变量
                            window.currentUser = user;
                            window.currentUserType = userType;
                        }
                    }
                } catch (e) {
                    console.error('读取本地存储失败:', e);
                }
            }

            // 3. 强制更新显示
            const permissionLevel = document.getElementById('permission-level');
            const permissionDescription = document.getElementById('permission-description');

            if (permissionLevel && permissionDescription) {
                const badgeText = getPermissionBadgeText(userType);
                const badgeColor = getPermissionBadgeColor(userType);

                permissionLevel.innerHTML = `<span style="color: ${badgeColor};">${badgeText}</span>`;

                // 设置权限描述
                let description = '';
                switch(userType) {
                    case 'admin':
                        description = '拥有完整系统权限，可查看详情、下载所有资料';
                        break;
                    case 'privileged':
                        description = '可查看详情、下载基础资料和PDF文档，可访问产品中心高级搜索';
                        break;
                    case 'premium':
                        description = '可查看产品详情、下载基础资料和PDF文档';
                        break;
                    case 'normal':
                        description = '可查看产品基础详情，管理个人信息';
                        break;
                    case 'guest':
                    default:
                        description = '可浏览基本产品信息，登录后可查看更多详情';
                        break;
                }

                permissionDescription.textContent = description;
                console.log('✅ 权限状态已更新:', { userType, badgeText, description });
            }

            // 重新显示产品以更新权限标识
            if (typeof currentCategory !== 'undefined') {
                displayProductsByCategory(currentCategory);
            }
        }

        // 加载产品数据
        async function loadProducts() {
            try {
                const { data: products, error } = await supabase
                    .from('products')
                    .select('*')
                    .order('created_at', { ascending: false });

                if (error) {
                    console.error('加载产品失败:', error);
                    return;
                }

                allProducts = products || [];
                console.log('加载了', allProducts.length, '个产品');

            } catch (error) {
                console.error('加载产品数据失败:', error);
            }
        }

        // 初始化分类切换功能
        function initializeCategorySwitch() {
            const categoryItems = document.querySelectorAll('.category-item');

            categoryItems.forEach(item => {
                item.addEventListener('click', function() {
                    // 移除所有active类
                    categoryItems.forEach(i => i.classList.remove('active'));

                    // 添加active类到当前项
                    this.classList.add('active');

                    // 获取选中的分类
                    const selectedCategory = this.getAttribute('data-category');
                    currentCategory = selectedCategory;

                    // 显示对应分类的产品
                    displayProductsByCategory(selectedCategory);
                });
            });
        }

        // 根据分类显示产品
        function displayProductsByCategory(category) {
            const container = document.getElementById('featured-products');

            // 获取该分类对应的数据库代码
            const categoryCodes = reverseCategoryMapping[category] || [];

            // 筛选产品
            const filteredProducts = allProducts.filter(product => {
                // 检查产品的分类代码是否匹配
                return categoryCodes.includes(product.product_category);
            });

            console.log(`筛选分类: ${category}, 代码: ${categoryCodes}, 找到产品: ${filteredProducts.length}`);

            if (filteredProducts.length === 0) {
                container.innerHTML = `
                    <div style="grid-column: 1 / -1; text-align: center; padding: 40px; color: #666;">
                        <p style="font-size: 16px;">暂无${category}产品</p>
                        <a href="products.html" style="color: #be131b; text-decoration: none;">查看所有产品 →</a>
                    </div>
                `;
                return;
            }

            // 显示产品（最多显示8个）
            const productsToShow = filteredProducts.slice(0, 8);

            container.innerHTML = productsToShow.map(product =>
                generateProductHTML(product)
            ).join('');
        }

        // 根据用户权限生成产品HTML
        function generateProductHTML(product) {
            const userType = getCurrentUserType();
            const canViewDetails = checkCanViewDetails();
            const canDownloadBasic = checkCanDownloadBasic();
            const canDownload = checkCanDownload();

            // 基础产品信息（所有用户都能看到）
            let productHTML = `
                <div class="product-item" onclick="handleProductClick('${product.id}')" style="border: 1px solid #e0e0e0; border-radius: 8px; overflow: hidden; transition: all 0.3s; cursor: pointer; position: relative;">
                    <div class="product-image" style="height: 150px; overflow: hidden; position: relative;">
                        <img src="${product.product_image || 'placeholder.svg'}"
                             alt="${product.product_name}"
                             onerror="this.src='placeholder.svg'"
                             style="width: 100%; height: 100%; object-fit: cover;">

                        <!-- 权限标识 -->
                        <div class="permission-badge" style="position: absolute; top: 8px; right: 8px; background: ${getPermissionBadgeColor(userType)}; color: white; padding: 2px 6px; border-radius: 4px; font-size: 10px;">
                            ${getPermissionBadgeText(userType)}
                        </div>
                    </div>

                    <div class="product-info" style="padding: 12px;">
                        <div class="product-name" style="font-weight: bold; margin-bottom: 4px; font-size: 14px;">${product.product_name}</div>
                        <div class="product-code" style="color: #666; font-size: 12px; margin-bottom: 8px;">${product.stock_code || ''}</div>
            `;

            // 移除所有操作按钮，只显示产品基本信息

            productHTML += `
                    </div>
                </div>
            `;

            return productHTML;
        }

        // 处理产品卡片点击事件
        function handleProductClick(productId) {
            const userType = getCurrentUserType();

            if (userType === 'guest') {
                // 游客：提示登录
                if (confirm('您需要登录后才能查看产品详情。是否前往登录页面？')) {
                    window.location.href = 'login.html';
                }
            } else {
                // 已登录用户：跳转到产品详情页
                window.location.href = `product-detail.html?id=${productId}`;
            }
        }

        // 权限检查函数
        function getCurrentUserType() {
            // 首先检查CustomAuth认证系统
            if (window.auth && window.auth.isInitialized) {
                const userType = window.auth.getUserType();
                console.log('从CustomAuth获取用户类型:', userType);
                if (userType && userType !== 'guest') {
                    return userType;
                }
            }

            // 检查全局变量
            if (window.currentUserType && window.currentUserType !== 'guest') {
                console.log('从全局变量获取用户类型:', window.currentUserType);
                return window.currentUserType;
            }

            // 如果全局变量是游客，检查本地存储
            try {
                const savedUser = localStorage.getItem('simple_auth_user');
                const loginTime = localStorage.getItem('simple_auth_login_time');

                if (savedUser && loginTime) {
                    // 检查登录是否过期（24小时）
                    const now = Date.now();
                    const loginTimestamp = parseInt(loginTime);
                    const expireTime = 24 * 60 * 60 * 1000; // 24小时

                    if (now - loginTimestamp < expireTime) {
                        const user = JSON.parse(savedUser);
                        console.log('从本地存储恢复用户权限:', user.username, user.user_type);

                        // 更新全局变量
                        window.currentUser = user;
                        window.currentUserType = user.user_type || 'premium';

                        return window.currentUserType;
                    }
                }
            } catch (e) {
                console.error('恢复用户权限失败:', e);
            }

            console.log('返回默认游客权限');
            return 'guest';
        }

        function checkCanViewDetails() {
            const userType = getCurrentUserType();
            return ['premium', 'privileged', 'admin'].includes(userType);
        }

        function checkCanDownloadBasic() {
            const userType = getCurrentUserType();
            return ['premium', 'privileged', 'admin'].includes(userType);
        }

        function checkCanDownload() {
            const userType = getCurrentUserType();
            return ['premium', 'privileged', 'admin'].includes(userType);
        }

        // 获取权限标识颜色
        function getPermissionBadgeColor(userType) {
            switch(userType) {
                case 'admin': return '#dc3545';
                case 'privileged': return '#6f42c1';
                case 'premium': return '#fd7e14';
                case 'normal': return '#28a745';
                case 'guest':
                default: return '#6c757d';
            }
        }

        // 获取权限标识文本
        function getPermissionBadgeText(userType) {
            switch(userType) {
                case 'admin': return '👑 管理员';
                case 'privileged': return '💎 特殊用户';
                case 'premium': return '⭐ 高级用户';
                case 'normal': return '👤 普通用户';
                case 'guest':
                default: return '👤 游客';
            }
        }

        // 显示登录提示
        function showLoginPrompt(event) {
            event.stopPropagation();
            if (confirm('您需要登录后才能查看产品详情。是否前往登录页面？')) {
                window.location.href = 'login.html';
            }
        }

        // 查看产品详情（注册用户及以上）
        function viewProductDetails(productId, event) {
            event.stopPropagation();
            if (!checkCanViewDetails()) {
                showLoginPrompt(event);
                return;
            }
            window.location.href = `product-detail.html?id=${productId}`;
        }

        // 下载基础资料（高级用户及以上）
        function downloadBasicFile(productId, event) {
            event.stopPropagation();
            if (!checkCanDownloadBasic()) {
                alert('您需要高级用户权限才能下载基础资料。请联系管理员升级权限。');
                return;
            }

            // 查找产品并下载基础资料
            const product = allProducts.find(p => p.id === productId);
            if (product && product.attachment_path) {
                // 检查是否是完整的URL
                let fileUrl = product.attachment_path;
                if (!fileUrl.startsWith('http')) {
                    // 如果是相对路径，构建完整的URL
                    fileUrl = `https://snckktsqwrbfwtjlvcfr.supabase.co/storage/v1/object/public/product-pdfs/${fileUrl}`;
                }

                // 在新窗口中打开文件
                window.open(fileUrl, '_blank');

                console.log(`下载基础资料: ${product.product_name} - ${fileUrl}`);
            } else {
                alert('该产品暂无可下载的基础资料。');
            }
        }

        // 下载PDF文档（特殊用户及以上）
        function downloadPDF(productId, event) {
            event.stopPropagation();

            console.log('🔽 downloadPDF 调用:', { productId, userType: getCurrentUserType() });

            if (!checkCanDownload()) {
                console.log('❌ 权限检查失败');
                alert('您需要高级用户权限才能下载PDF文档。请联系管理员升级权限。');
                return;
            }

            console.log('✅ 权限检查通过');
            console.log('📦 allProducts 数量:', allProducts.length);

            // 查找产品并下载PDF
            const product = allProducts.find(p => p.id === productId);
            console.log('🔍 查找产品结果:', { productId, found: !!product, product });

            if (product && product.attachment_path) {
                let fileUrl = product.attachment_path;

                // 检查是否是完整的URL
                if (!fileUrl.startsWith('http')) {
                    // 如果是相对路径，构建完整的URL
                    fileUrl = `https://snckktsqwrbfwtjlvcfr.supabase.co/storage/v1/object/public/product-pdfs/${fileUrl}`;
                }

                console.log(`尝试打开文档: ${product.product_name} - ${fileUrl}`);

                // 检查文件扩展名
                const fileExtension = fileUrl.split('.').pop().toLowerCase();

                if (fileExtension === 'pdf') {
                    // PDF文件：在新窗口中打开
                    window.open(fileUrl, '_blank');
                } else if (fileExtension === 'html') {
                    // HTML文件：在新窗口中打开
                    window.open(fileUrl, '_blank');
                } else {
                    // 其他文件：尝试下载
                    const link = document.createElement('a');
                    link.href = fileUrl;
                    link.download = `${product.product_name}_文档.${fileExtension}`;
                    link.target = '_blank';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                }

                console.log(`已打开文档: ${product.product_name} - ${fileUrl}`);
            } else {
                alert('该产品暂无可下载的PDF文档。');
            }
        }

        // 查看产品详情（保持向后兼容）
        function viewProduct(productId) {
            viewProductDetails(productId, { stopPropagation: () => {} });
        }

        // 应用首页筛选
        function applyHomeFilters() {
            const brandFilter = document.getElementById('brand-filter').value.trim().toLowerCase();
            const modelFilter = document.getElementById('model-filter').value.trim().toLowerCase();
            const productFilter = document.getElementById('product-filter').value.trim().toLowerCase();

            // 如果所有筛选条件都为空，显示当前分类的产品
            if (!brandFilter && !modelFilter && !productFilter) {
                displayProductsByCategory(currentCategory);
                return;
            }

            const container = document.getElementById('featured-products');

            // 筛选产品
            const filteredProducts = allProducts.filter(product => {
                // 分类筛选（保持当前选中的分类）
                const categoryCodes = reverseCategoryMapping[currentCategory] || [];
                const categoryMatch = categoryCodes.includes(product.product_category);

                // 品牌筛选（在产品名称、规格、材料中搜索）
                const brandMatch = !brandFilter ||
                    product.product_name.toLowerCase().includes(brandFilter) ||
                    (product.specifications && product.specifications.toLowerCase().includes(brandFilter)) ||
                    (product.material && product.material.toLowerCase().includes(brandFilter)) ||
                    (product.stock_code && product.stock_code.toLowerCase().includes(brandFilter));

                // 车型筛选（在产品名称、规格中搜索）
                const modelMatch = !modelFilter ||
                    product.product_name.toLowerCase().includes(modelFilter) ||
                    (product.specifications && product.specifications.toLowerCase().includes(modelFilter)) ||
                    (product.stock_code && product.stock_code.toLowerCase().includes(modelFilter));

                // 产品筛选（在产品名称、编号中搜索）
                const productMatch = !productFilter ||
                    product.product_name.toLowerCase().includes(productFilter) ||
                    (product.stock_code && product.stock_code.toLowerCase().includes(productFilter)) ||
                    (product.data_id && product.data_id.toLowerCase().includes(productFilter));

                return categoryMatch && brandMatch && modelMatch && productMatch;
            });

            console.log(`筛选结果: 品牌="${brandFilter}", 车型="${modelFilter}", 产品="${productFilter}", 找到产品: ${filteredProducts.length}`);

            if (filteredProducts.length === 0) {
                container.innerHTML = `
                    <div style="grid-column: 1 / -1; text-align: center; padding: 40px; color: #666;">
                        <p style="font-size: 16px;">没有找到符合条件的产品</p>
                        <p style="font-size: 14px; color: #999;">请尝试调整筛选条件</p>
                        <button onclick="resetHomeFilters()" style="background: #be131b; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-top: 10px;">重置筛选</button>
                    </div>
                `;
                return;
            }

            // 显示筛选结果（最多显示8个）
            const productsToShow = filteredProducts.slice(0, 8);

            container.innerHTML = productsToShow.map(product =>
                generateProductHTML(product)
            ).join('');
        }

        // 重置首页筛选
        function resetHomeFilters() {
            document.getElementById('brand-filter').value = '';
            document.getElementById('model-filter').value = '';
            document.getElementById('product-filter').value = '';

            // 显示当前分类的产品
            displayProductsByCategory(currentCategory);
        }

        // 为筛选输入框添加回车键支持
        document.addEventListener('DOMContentLoaded', function() {
            const filterInputs = ['brand-filter', 'model-filter', 'product-filter'];

            filterInputs.forEach(inputId => {
                const input = document.getElementById(inputId);
                if (input) {
                    input.addEventListener('keypress', function(e) {
                        if (e.key === 'Enter') {
                            applyHomeFilters();
                        }
                    });
                }
            });
        });
    </script>

    <!-- 客服系统脚本 -->
    <script src="js/customer-service-chat.js"></script>
</body>
</html>
