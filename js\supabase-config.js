// Supabase 配置
if (typeof window.SUPABASE_URL === 'undefined') {
    window.SUPABASE_URL = 'https://snckktsqwrbfwtjlvcfr.supabase.co';
    window.SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNuY2trdHNxd3JiZnd0amx2Y2ZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMDAwMDksImV4cCI6MjA2NjY3NjAwOX0.L83td9BHYz7Z6vQke7CXPZrcOXcQ8FKg9duBL-fm644';
}

// 初始化 Supabase 客户端 - 优先使用标准客户端
if (typeof window.supabaseClient === 'undefined') {
    console.log('🔧 [SUPABASE-CONFIG] 初始化Supabase客户端');

    // 优先使用标准 Supabase 客户端
    if (typeof window.supabase !== 'undefined' && typeof window.supabase.createClient === 'function') {
        window.supabaseClient = window.supabase.createClient(window.SUPABASE_URL, window.SUPABASE_ANON_KEY);
        console.log('🔧 [SUPABASE-CONFIG] 标准Supabase客户端初始化完成');
    } else {
        console.error('🔧 [SUPABASE-CONFIG] Supabase库未加载');
    }
}

// 设置全局引用（只有在未定义时才设置）
if (typeof window.supabase === 'undefined' || typeof window.supabase.createClient !== 'function') {
    window.supabase = window.supabaseClient;
}
const supabase = window.supabaseClient || window.supabase;

// 用户权限类型 - 统一五级权限系统
const USER_TYPES = {
    GUEST: 'guest',           // 游客（未注册用户）
    NORMAL: 'normal',         // 普通用户（新注册用户默认）
    PREMIUM: 'premium',       // 高级用户（可查看详情和下载基础资料）
    PRIVILEGED: 'privileged', // 特许用户（可下载PDF）
    ADMIN: 'admin'            // 管理员（完全权限）
};

// 兼容旧版本
USER_TYPES.NORMAL = USER_TYPES.PREMIUM;

// 当前用户信息
let currentUser = null;
let currentUserType = USER_TYPES.GUEST; // 默认为游客

// 更新用户访问统计
async function updateUserVisitStats(userId) {
    try {
        console.log('📊 更新用户访问统计:', userId);

        // 先获取当前访问次数
        const { data: currentUser, error: fetchError } = await supabase
            .from('users')
            .select('visit_count')
            .eq('id', userId)
            .single();

        if (fetchError) {
            console.error('获取当前访问次数失败:', fetchError);
            return;
        }

        const currentCount = currentUser?.visit_count || 0;

        // 更新访问次数和最近访问时间
        const { error } = await supabase
            .from('users')
            .update({
                visit_count: currentCount + 1,
                last_visit_at: new Date().toISOString()
            })
            .eq('id', userId);

        if (error) {
            console.error('更新访问统计失败:', error);
        } else {
            console.log('✅ 访问统计更新成功，新访问次数:', currentCount + 1);
        }
    } catch (error) {
        console.error('更新访问统计异常:', error);
    }
}

// 初始化客服系统（本地模拟版本）
function initCustomerService() {
    console.log('客服系统已初始化 (本地模拟模式)');

    // 如果没有客服消息历史，创建一个欢迎消息
    let messages = JSON.parse(localStorage.getItem('customer_service_messages') || '[]');
    if (messages.length === 0) {
        const welcomeMessage = {
            id: Date.now(),
            user_id: null,
            user_name: '系统',
            user_email: null,
            message_content: '欢迎使用春晟机械客服系统！我们将竭诚为您服务。',
            message_type: 'admin',
            created_at: new Date().toISOString(),
            is_read: false
        };
        messages.push(welcomeMessage);
        localStorage.setItem('customer_service_messages', JSON.stringify(messages));
    }
}

// 用户认证相关函数 - 使用简化认证
async function signUp(email, password, userData) {
    try {
        // 检查用户是否已存在
        const { data: existingUser } = await supabase
            .from('users')
            .select('email')
            .eq('email', email)
            .single();

        if (existingUser) {
            return { data: null, error: { message: '该邮箱已被注册' } };
        }

        // 创建新用户记录
        const newUser = {
            username: userData.username,
            email: email,
            password_hash: btoa(password + 'chunsheng_salt'), // 简单哈希
            user_type: userData.user_type || USER_TYPES.PREMIUM,
            company_name: userData.company_name,
            phone: userData.phone,
            is_active: true
        };

        const { data, error } = await supabase
            .from('users')
            .insert([newUser])
            .select()
            .single();

        if (error) throw error;

        return { data, error: null };
    } catch (error) {
        return { data: null, error };
    }
}

async function signIn(email, password) {
    try {
        // 查询用户
        const { data: user, error } = await supabase
            .from('users')
            .select('*')
            .eq('email', email)
            .eq('is_active', true)
            .single();

        if (error || !user) {
            return { data: null, error: { message: '用户不存在或已被禁用' } };
        }

        if (!user.password_hash) {
            return { data: null, error: { message: '用户密码未设置' } };
        }

        // 验证密码
        const hashedPassword = btoa(password + 'chunsheng_salt');
        if (hashedPassword !== user.password_hash) {
            return { data: null, error: { message: '密码错误' } };
        }

        // 登录成功，更新访问统计
        await updateUserVisitStats(user.id);

        // 设置当前用户
        currentUser = user;
        currentUserType = user.user_type || USER_TYPES.PREMIUM;

        // 保存到本地存储
        localStorage.setItem('simple_auth_user', JSON.stringify(user));
        localStorage.setItem('simple_auth_login_time', Date.now().toString());

        updateUIForUser();

        return { data: { user }, error: null };
    } catch (error) {
        return { data: null, error };
    }
}

async function signOut() {
    try {
        // 清除本地存储
        localStorage.removeItem('simple_auth_user');
        localStorage.removeItem('simple_auth_login_time');

        currentUser = null;
        currentUserType = USER_TYPES.GUEST;
        updateUIForUser();

        return { error: null };
    } catch (error) {
        return { error };
    }
}

async function getCurrentUserInfo() {
    try {
        console.log('开始获取用户信息...');

        // 从本地存储检查登录状态
        const savedUser = localStorage.getItem('simple_auth_user');
        const loginTime = localStorage.getItem('simple_auth_login_time');

        if (savedUser && loginTime) {
            // 检查登录是否过期（24小时）
            const now = Date.now();
            const loginTimestamp = parseInt(loginTime);
            const expireTime = 24 * 60 * 60 * 1000; // 24小时

            if (now - loginTimestamp < expireTime) {
                const user = JSON.parse(savedUser);
                console.log('恢复登录状态:', user.username);

                currentUser = user;
                currentUserType = user.user_type || USER_TYPES.PREMIUM;

                console.log('设置用户类型:', currentUserType);
                console.log('用户权限详情:', {
                    canViewDetails: canViewDetails(),
                    canDownload: canDownload(),
                    canDownloadBasic: canDownloadBasic()
                });
                updateUIForUser();
                return currentUser;
            } else {
                // 登录已过期，清除存储
                localStorage.removeItem('simple_auth_user');
                localStorage.removeItem('simple_auth_login_time');
            }
        }

        console.log('没有有效的登录状态，设置为游客');
        currentUser = null;
        currentUserType = USER_TYPES.GUEST;
        updateUIForUser();
        return null;

    } catch (error) {
        console.error('获取用户信息失败:', error);
        currentUser = null;
        currentUserType = USER_TYPES.GUEST;
        updateUIForUser();
        return null;
    }
}

// 示例产品数据（当数据库为空时使用）
const sampleProducts = [
    {
        id: 1,
        data_id: 'A0001',
        stock_code: 'CS0001',
        product_name: '减震器上盖',
        product_category: 'DB',
        specifications: 'φ78×45',
        material: 'SPHC',
        thickness: '2.5',
        remarks: '适用于前减震器',
        shape_code: '1001',
        main_process_1: '冲孔',
        main_process_2: '拉深',
        main_process_3: '修边',
        main_process_4: '',
        process_count: 3,
        variable_process_1: '去毛刺',
        variable_process_2: '表面处理',
        variable_process_3: '',
        attachment_path: 'pdfs/A0001_减震器上盖.pdf',
        created_at: '2024-01-15T10:00:00Z'
    },
    {
        id: 2,
        data_id: 'A0002',
        stock_code: 'CS0002',
        product_name: '减震器下座',
        product_category: 'DB',
        specifications: 'φ85×50',
        material: 'SPHC',
        thickness: '3.0',
        remarks: '适用于后减震器',
        shape_code: '1002',
        main_process_1: '落料',
        main_process_2: '拉深',
        main_process_3: '冲孔',
        main_process_4: '修边',
        process_count: 4,
        variable_process_1: '去毛刺',
        variable_process_2: '焊接',
        variable_process_3: '',
        attachment_path: 'pdfs/A0002_减震器下座.pdf',
        created_at: '2024-01-16T10:00:00Z'
    },
    {
        id: 3,
        data_id: 'A0003',
        stock_code: 'CS0003',
        product_name: '弹簧座圈',
        product_category: 'DB',
        specifications: 'φ65×25',
        material: 'SPCC',
        thickness: '2.0',
        remarks: '弹簧固定座',
        shape_code: '1003',
        main_process_1: '冲孔',
        main_process_2: '成型',
        main_process_3: '',
        main_process_4: '',
        process_count: 2,
        variable_process_1: '倒角',
        variable_process_2: '',
        variable_process_3: '',
        attachment_path: 'pdfs/A0003_弹簧座圈.pdf',
        created_at: '2024-01-17T10:00:00Z'
    },
    {
        id: 4,
        data_id: 'A0004',
        inventory_code: 'CS0004',
        product_name: '活塞杆导向套',
        product_category: 'DB',
        product_specs: 'φ42×80',
        material: 'SPHC',
        thickness: '2.8',
        notes: '活塞杆导向部件',
        shape_code: '1004',
        main_process_1: '拉深',
        main_process_2: '冲孔',
        main_process_3: '车削',
        main_process_4: '热处理',
        process_count: 4,
        variable_process_1: '精加工',
        variable_process_2: '表面镀层',
        variable_process_3: '',
        attachment_path: 'pdfs/A0004_活塞杆导向套.pdf',
        user_access_level: 'premium',
        created_at: '2024-01-18T10:00:00Z'
    },
    {
        id: 5,
        data_id: 'A0005',
        inventory_code: 'CS0005',
        product_name: '减震器外筒',
        product_category: 'DB',
        product_specs: 'φ95×200',
        material: 'SPHC',
        thickness: '3.5',
        notes: '减震器主体外筒',
        shape_code: '1005',
        main_process_1: '卷圆',
        main_process_2: '焊接',
        main_process_3: '车削',
        main_process_4: '热处理',
        process_count: 4,
        variable_process_1: '内表面处理',
        variable_process_2: '密封检测',
        variable_process_3: '',
        attachment_path: 'pdfs/A0005_减震器外筒.pdf',
        user_access_level: 'premium',
        created_at: '2024-01-19T10:00:00Z'
    },
    {
        id: 6,
        data_id: 'A0006',
        inventory_code: 'CS0006',
        product_name: '缓冲块支架',
        product_category: 'DB',
        product_specs: 'φ55×35',
        material: 'SPCC',
        thickness: '2.2',
        notes: '缓冲块固定支架',
        shape_code: '1006',
        main_process_1: '冲孔',
        main_process_2: '弯曲',
        main_process_3: '修边',
        main_process_4: '',
        process_count: 3,
        variable_process_1: '去毛刺',
        variable_process_2: '喷涂',
        variable_process_3: '',
        attachment_path: 'pdfs/A0006_缓冲块支架.pdf',
        user_access_level: 'normal',
        created_at: '2024-01-20T10:00:00Z'
    }
];

// 产品相关函数
async function getProducts(limit = null) {
    console.log('开始获取产品数据...');

    try {
        // 尝试从数据库获取产品
        console.log('尝试从 Supabase 数据库获取产品...');

        // 获取排序设置（如果有的话）
        let sortField = 'created_at';
        let sortOrder = false; // false = descending (最新的在前)

        // 检查是否有管理员设置的排序规则
        try {
            const { data: settingsData } = await supabase
                .from('admin_settings')
                .select('setting_key, setting_value')
                .eq('setting_key', 'product_sort_order')
                .single();

            if (settingsData && settingsData.setting_value) {
                const sortSettings = JSON.parse(settingsData.setting_value);
                sortField = sortSettings.field || 'created_at';
                sortOrder = sortSettings.ascending || false;
                console.log('使用管理员设置的排序:', sortField, sortOrder ? 'ASC' : 'DESC');
            }
        } catch (settingsError) {
            console.log('未找到排序设置，使用默认排序');
        }

        let query = supabase
            .from('products')
            .select('*')
            .order(sortField, { ascending: sortOrder });

        if (limit) {
            query = query.limit(limit);
        }

        const { data, error } = await query;

        if (error) {
            console.warn('数据库查询失败:', error.message);
            console.log('错误详情:', error);
            console.log('使用示例数据作为备用');
            let result = [...sampleProducts];
            if (limit) {
                result = result.slice(0, limit);
            }
            return result;
        }

        // 如果数据库有数据，返回数据库数据
        if (data && data.length > 0) {
            console.log(`✓ 从数据库成功获取 ${data.length} 个产品`);
            return data;
        }

        // 如果数据库为空，返回示例数据
        console.log('数据库为空，使用示例数据');
        let result = [...sampleProducts];
        if (limit) {
            result = result.slice(0, limit);
        }
        return result;

    } catch (error) {
        console.error('获取产品过程出错:', error);
        console.log('使用示例数据作为备用');
        let result = [...sampleProducts];
        if (limit) {
            result = result.slice(0, limit);
        }
        return result;
    }
}

async function getProductById(id) {
    try {
        const { data, error } = await supabase
            .from('products')
            .select('*')
            .eq('id', id)
            .single();

        if (error) throw error;
        return data;
    } catch (error) {
        console.error('获取产品详情失败:', error);
        return null;
    }
}

// 获取车型数据（从产品的car_models字段中提取）
async function getCarModels() {
    try {
        console.log('开始从产品数据中获取车型信息...');

        // 获取所有产品的车型信息
        const { data: products, error } = await supabase
            .from('products')
            .select('car_models')
            .not('car_models', 'is', null);

        if (error) {
            console.warn('产品车型数据查询失败:', error.message);
            console.log('使用示例车型数据作为备用');
            return getSampleCarModels();
        }

        if (products && products.length > 0) {
            // 从产品的car_models字段中提取所有车型
            const allCarModels = new Set();
            const carModelsByBrand = {};

            products.forEach(product => {
                if (product.car_models) {
                    const models = product.car_models.split(',').map(model => model.trim());
                    models.forEach(model => {
                        if (model) {
                            allCarModels.add(model);
                            // 提取品牌名
                            const brandMatch = model.match(/^([\u4e00-\u9fa5]+)/);
                            if (brandMatch) {
                                const brand = brandMatch[1];
                                if (!carModelsByBrand[brand]) {
                                    carModelsByBrand[brand] = [];
                                }
                                carModelsByBrand[brand].push(model);
                            }
                        }
                    });
                }
            });

            // 转换为标准格式
            const result = [];
            let id = 1;
            Object.keys(carModelsByBrand).sort().forEach(brand => {
                const firstLetter = brand.charAt(0);
                carModelsByBrand[brand].forEach(model => {
                    result.push({
                        id: id++,
                        brand: brand,
                        brand_name: model,
                        model_name: model,
                        first_letter: firstLetter
                    });
                });
            });

            console.log(`✓ 从产品数据成功提取 ${result.length} 个车型`);
            return result;
        }

        console.log('没有找到车型数据，使用示例车型数据');
        return getSampleCarModels();

    } catch (error) {
        console.error('获取车型过程出错:', error);
        console.log('使用示例车型数据作为备用');
        return getSampleCarModels();
    }
}

// 示例车型数据
function getSampleCarModels() {
    return [
        {
            id: 1,
            brand: '奥迪',
            brand_name: '奥迪 A4L',
            model_name: '2020款 40 TFSI 进取型',
            first_letter: 'A',
            product_id: 'A0001'
        },
        {
            id: 2,
            brand: '宝马',
            brand_name: '宝马 3系',
            model_name: '2021款 325Li M运动套装',
            first_letter: 'B',
            product_id: 'A0002'
        },
        {
            id: 3,
            brand: '奔驰',
            brand_name: '奔驰 C级',
            model_name: '2020款 C 260 L 运动版',
            first_letter: 'B',
            product_id: 'A0003'
        },
        {
            id: 4,
            brand: '大众',
            brand_name: '大众 帕萨特',
            model_name: '2021款 280TSI 商务版',
            first_letter: 'D',
            product_id: 'A0004'
        },
        {
            id: 5,
            brand: '丰田',
            brand_name: '丰田 凯美瑞',
            model_name: '2021款 2.0G 豪华版',
            first_letter: 'F',
            product_id: 'A0005'
        }
    ];
}



// 下载记录函数
async function recordDownload(productId, fileType) {
    if (!currentUser) return;

    try {
        const { error } = await supabase
            .from('download_records')
            .insert([{
                user_id: currentUser.id,
                product_id: productId,
                file_type: fileType
            }]);

        if (error) throw error;
    } catch (error) {
        console.error('记录下载失败:', error);
    }
}

// ==================== 简化的客服系统相关函数 ====================

// 发送客服消息
async function sendCustomerServiceMessage(messageContent, guestInfo = null) {
    try {
        let messageData;

        if (currentUser) {
            // 已登录用户
            messageData = {
                user_id: currentUser.id,
                user_name: currentUser.username,
                user_email: currentUser.email,
                message_content: messageContent,
                message_type: 'user'
            };
        } else {
            // 游客用户
            messageData = {
                user_id: null,
                user_name: guestInfo?.name || '游客',
                user_email: guestInfo?.email || null,
                message_content: messageContent,
                message_type: 'user'
            };
        }

        const { data, error } = await supabase
            .from('customer_service_messages')
            .insert([messageData])
            .select()
            .single();

        if (error) throw error;
        console.log('客服消息已发送:', data);
        return data;
    } catch (error) {
        console.error('发送客服消息失败:', error);
        return null;
    }
}

// 获取用户的客服消息历史
async function getUserCustomerServiceMessages(limit = 50) {
    if (!currentUser) return [];

    try {
        const { data, error } = await supabase
            .from('customer_service_messages')
            .select('*')
            .eq('user_id', currentUser.id)
            .order('created_at', { ascending: true })
            .limit(limit);

        if (error) throw error;
        return data || [];
    } catch (error) {
        console.error('获取客服消息失败:', error);
        return [];
    }
}

// 标记用户消息为已读（管理员回复后）
async function markUserMessagesAsRead(userId) {
    try {
        const { error } = await supabase
            .from('customer_service_messages')
            .update({ is_read: true })
            .eq('user_id', userId)
            .eq('message_type', 'admin');

        if (error) throw error;
        console.log('消息已标记为已读');
    } catch (error) {
        console.error('标记消息已读失败:', error);
    }
}

// UI更新函数
function updateUIForUser() {
    const loginBtn = document.querySelector('.login-btn');
    const registerBtn = document.querySelector('.register-btn');

    // 更新全局权限变量
    window.currentUserType = currentUserType;
    window.currentUser = currentUser;

    console.log('🔄 [UI-UPDATE] 更新全局权限变量:', {
        currentUserType,
        currentUser: currentUser ? currentUser.username : null
    });

    if (currentUser) {
        if (loginBtn) {
            loginBtn.textContent = currentUser.username;
            loginBtn.href = '#';
            loginBtn.onclick = showUserMenu;
        }
        if (registerBtn) {
            registerBtn.textContent = '退出';
            registerBtn.onclick = signOut;
        }
    } else {
        if (loginBtn) {
            loginBtn.textContent = '登录';
            loginBtn.href = 'login.html';
            loginBtn.onclick = null;
        }
        if (registerBtn) {
            registerBtn.textContent = '注册';
            registerBtn.onclick = null;
        }
    }

    // 触发权限状态变化事件
    if (typeof window.updatePermissionStatus === 'function') {
        window.updatePermissionStatus();
    }

    // 触发自定义事件，通知其他组件权限状态已更新
    window.dispatchEvent(new CustomEvent('authReady', {
        detail: { userType: currentUserType, user: currentUser }
    }));
}

function showUserMenu() {
    // 显示用户菜单
    if (currentUser) {
        alert(`用户：${currentUser.username}\n权限：${getUserTypeDisplay()}\n公司：${currentUser.company_name || '未填写'}`);
    } else {
        alert(`当前状态：${getUserTypeDisplay()}\n提示：注册登录后可获得更多权限`);
    }
}

// 用户注册函数 - 简化版本
async function registerUser(userData) {
    try {
        console.log('开始注册用户:', userData.email);

        // 检查用户是否已存在
        const { data: existingUser } = await supabase
            .from('users')
            .select('email')
            .eq('email', userData.email)
            .single();

        if (existingUser) {
            return { success: false, message: '该邮箱已被注册' };
        }

        // 创建新用户记录
        const newUser = {
            username: userData.username || `${userData.firstName}${userData.lastName}`,
            email: userData.email,
            password_hash: btoa(userData.password + 'chunsheng_salt'),
            user_type: userData.userType || 'premium',
            first_name: userData.firstName || '',
            last_name: userData.lastName || '',
            phone: userData.phone || '',
            company_name: userData.company || '',
            is_active: true
        };

        const { data: user, error } = await supabase
            .from('users')
            .insert([newUser])
            .select()
            .single();

        if (error) {
            console.error('用户记录创建失败:', error);
            return { success: false, message: '注册失败: ' + error.message };
        }

        console.log('用户注册成功:', user.username);
        return {
            success: true,
            message: '注册成功！您现在可以登录了。',
            user: user
        };

    } catch (error) {
        console.error('注册过程出错:', error);
        return { success: false, message: '注册失败，请稍后重试' };
    }
}

// 用户登录函数 - 简化版本
async function loginUser(email, password) {
    try {
        console.log('开始登录:', email);

        // 查询用户
        const { data: user, error } = await supabase
            .from('users')
            .select('*')
            .eq('email', email)
            .eq('is_active', true)
            .single();

        if (error || !user) {
            console.error('用户查询失败:', error);
            return { success: false, message: '用户不存在或已被禁用' };
        }

        if (!user.password_hash) {
            return { success: false, message: '用户密码未设置，请联系管理员' };
        }

        // 验证密码
        const hashedPassword = btoa(password + 'chunsheng_salt');
        if (hashedPassword !== user.password_hash) {
            return { success: false, message: '密码错误' };
        }

        // 登录成功，更新访问统计
        await updateUserVisitStats(user.id);

        // 设置当前用户
        currentUser = user;
        currentUserType = user.user_type || USER_TYPES.PREMIUM;

        // 保存到本地存储
        localStorage.setItem('simple_auth_user', JSON.stringify(user));
        localStorage.setItem('simple_auth_login_time', Date.now().toString());

        updateUIForUser();

        console.log('登录成功:', user.username);
        return {
            success: true,
            message: '登录成功！',
            user: user
        };

    } catch (error) {
        console.error('登录过程出错:', error);
        return { success: false, message: '登录失败，请稍后重试' };
    }
}

// 用户登出函数 - 简化版本
async function logoutUser() {
    try {
        // 清除本地存储
        localStorage.removeItem('simple_auth_user');
        localStorage.removeItem('simple_auth_login_time');

        // 清除当前用户信息
        currentUser = null;
        currentUserType = USER_TYPES.GUEST;
        updateUIForUser();

        console.log('登出成功');
        return { success: true, message: '登出成功！' };

    } catch (error) {
        console.error('登出过程出错:', error);
        return { success: false, message: '登出失败，请稍后重试' };
    }
}

// 权限检查函数
function canViewDetails() {
    // 高级用户及以上可以查看详细信息
    return [USER_TYPES.PREMIUM, USER_TYPES.PRIVILEGED, USER_TYPES.ADMIN].includes(currentUserType);
}

function canViewBasicDetails() {
    // 普通用户及以上可以查看基础详细信息
    return [USER_TYPES.NORMAL, USER_TYPES.PREMIUM, USER_TYPES.PRIVILEGED, USER_TYPES.ADMIN].includes(currentUserType);
}

function canDownload() {
    // 高级用户及以上可以下载PDF文档
    return [USER_TYPES.PREMIUM, USER_TYPES.PRIVILEGED, USER_TYPES.ADMIN].includes(currentUserType);
}

function canDownloadBasic() {
    // 高级用户及以上可以下载基础资料
    return [USER_TYPES.PREMIUM, USER_TYPES.PRIVILEGED, USER_TYPES.ADMIN].includes(currentUserType);
}

function canDownloadBasic() {
    // 高级用户及以上可以下载基础资料
    return [USER_TYPES.PREMIUM, USER_TYPES.PRIVILEGED, USER_TYPES.ADMIN].includes(currentUserType);
}

function getUserTypeDisplay() {
    switch(currentUserType) {
        case USER_TYPES.GUEST: return '游客';
        case USER_TYPES.PREMIUM: return '高级用户';
        case USER_TYPES.PRIVILEGED: return '特许用户';
        case USER_TYPES.ADMIN: return '管理员';
        default: return '未知';
    }
}

// 同步AuthManager状态到全局变量
function syncAuthManagerToGlobal() {
    if (typeof window.authManager !== 'undefined') {
        const authUser = window.authManager.getCurrentUser();
        const authUserType = window.authManager.getUserType();

        if (authUser && authUserType !== 'guest') {
            console.log('同步AuthManager状态到全局变量:', authUser, authUserType);
            currentUser = authUser;
            currentUserType = authUserType;
            updateUIForUser();
            return true;
        }
    }
    return false;
}

// 初始化
document.addEventListener('DOMContentLoaded', async function() {
    // 等待AuthManager初始化
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 先尝试同步AuthManager状态
    if (!syncAuthManagerToGlobal()) {
        // 如果AuthManager没有用户，检查Supabase登录状态
        await getCurrentUserInfo();
    }

    // 简化认证系统不需要监听Auth状态变化
    console.log('🔐 [SIMPLE-AUTH] 简化认证系统已启用，跳过Auth状态监听');

    // 定期同步AuthManager状态（处理传统管理员登录）
    setInterval(() => {
        if (!currentUser || currentUserType === USER_TYPES.GUEST) {
            syncAuthManagerToGlobal();
        }
    }, 2000);
});
